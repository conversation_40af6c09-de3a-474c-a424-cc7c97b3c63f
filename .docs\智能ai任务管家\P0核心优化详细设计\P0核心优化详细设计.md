# P0 核心优化详细设计文档

## 📋 项目背景

### 背景分析

基于现有智能任务管家功能的深度优化，解决当前架构复杂度过高、用户体验不够友好的核心矛盾。通过状态管理简化和错误处理优化，为后续 P1 核心矛盾解决奠定稳固基础。

### 核心矛盾

**技术复杂度 vs 用户体验简洁性**

- **对立面 A**：系统需要处理复杂的 AI 交互流程，包括 10 种 SSE 消息类型、多状态工具执行、异步流式处理
- **对立面 B**：用户期望简单直观的交互体验，清晰的状态反馈，友好的错误提示
- **载体转化机会**：通过架构简化实现技术复杂度的内聚，向用户呈现简洁统一的状态模型

## 🎯 需求细化

### P0-1: 状态管理简化

#### 当前问题分析

**后端复杂度**：

- 10 种 SSE 消息类型：`processing_start`、`session_end`、`error`、`chat_content_chunk`、`tool_call_start`、`tool_execution_start`、`tool_execution_complete`、`tool_execution_error`、`tool_result_processing`、`tool_result_error`
- 状态机复杂，调试困难

**前端复杂度**：

- aiState 对象包含 150+行复杂状态结构
- 多层嵌套状态：`loading`、`streaming`、`toolExecution`
- 状态同步逻辑分散，维护成本高

#### 解决方案设计

**简化状态模型**：将 10 种 SSE 消息类型映射为 4 种核心用户状态

```javascript
// 新的简化状态模型
const SIMPLE_STATES = {
  IDLE: 'idle', // 空闲状态
  THINKING: 'thinking', // AI思考中（包含分析、准备阶段）
  EXECUTING: 'executing', // 工具执行中
  RESPONDING: 'responding', // 生成回复中
}

// 简化的前端状态管理
const aiState = {
  status: 'idle',
  message: '',
  progress: null,
  sessionId: null,
}
```

**状态映射表**：

| 原 SSE 消息类型                       | 新状态     | 用户看到的提示        |
| ------------------------------------- | ---------- | --------------------- |
| processing_start                      | THINKING   | "正在理解您的需求..." |
| tool_call_start, tool_execution_start | EXECUTING  | "正在执行操作..."     |
| tool_result_processing                | RESPONDING | "正在生成回复..."     |
| chat_content_chunk                    | RESPONDING | "正在回复..."         |
| session_end, error                    | IDLE       | 恢复空闲状态          |

### P0-2: 错误处理优化

#### 当前问题分析

- 错误信息技术化：如"SSE Channel error"、"Tool execution failed"
- 错误类型过多，处理不一致
- 缺少用户友好的错误恢复机制

#### 解决方案设计

**统一错误分类体系**：

```javascript
const USER_ERRORS = {
  NETWORK: {
    message: '网络连接不稳定，请检查网络后重试',
    action: 'retry',
    icon: 'wifi-off',
  },
  AUTH: {
    message: '登录已过期，请重新登录',
    action: 'login',
    icon: 'lock',
  },
  PARSE: {
    message: '我没理解您的意思，能换个说法吗？',
    action: 'rephrase',
    icon: 'help-circle',
  },
  SYSTEM: {
    message: '系统暂时繁忙，请稍后重试',
    action: 'retry',
    icon: 'alert-circle',
  },
}
```

## 🏗️ 技术方案

### 架构设计

```mermaid
graph TD
    A[用户输入] --> B[前端状态管理器]
    B --> C[SSE消息处理器]
    C --> D[状态映射器]
    D --> E[UI状态更新]

    F[后端SSE消息] --> C
    C --> G[错误分类器]
    G --> H[用户友好错误处理]
    H --> E

    subgraph "简化状态模型"
        I[IDLE]
        J[THINKING]
        K[EXECUTING]
        L[RESPONDING]
    end

    D --> I
    D --> J
    D --> K
    D --> L
```

### 核心实现方案

#### 1. 后端状态映射层

**新增状态映射模块** (`modules/state-mapper.js`):

```javascript
const STATE_MAPPING = {
  processing_start: { state: 'THINKING', message: '正在理解您的需求...' },
  tool_call_start: { state: 'EXECUTING', message: '正在执行操作...' },
  tool_execution_start: { state: 'EXECUTING', message: '正在执行操作...' },
  tool_result_processing: { state: 'RESPONDING', message: '正在生成回复...' },
  chat_content_chunk: { state: 'RESPONDING', message: '正在回复...' },
  session_end: { state: 'IDLE', message: '' },
  error: { state: 'IDLE', message: '' },
}

function mapToSimpleState(sseType, originalData) {
  const mapping = STATE_MAPPING[sseType]
  if (!mapping) return null

  return {
    type: 'simple_state_update',
    data: {
      state: mapping.state,
      message: mapping.message,
      progress: originalData.progress || null,
    },
  }
}
```

#### 2. 前端状态管理重构

**简化 aiState 结构**：

```javascript
// 重构后的简化状态
const aiState = ref({
  status: 'idle', // idle/thinking/executing/responding
  message: '', // 当前状态描述
  progress: null, // 进度信息（可选）
  sessionId: null, // 会话ID
})

// 状态更新函数
const updateAiState = (newState, message = '', progress = null) => {
  aiState.value.status = newState
  aiState.value.message = message
  aiState.value.progress = progress
}
```

#### 3. 错误处理统一化

**错误映射和处理**：

```javascript
// 错误码映射表
const ERROR_MAPPING = {
  NETWORK_ERROR: 'NETWORK',
  TIMEOUT: 'NETWORK',
  UNAUTHORIZED: 'AUTH',
  PARSE_ERROR: 'PARSE',
  SYSTEM_ERROR: 'SYSTEM',
}

// 统一错误处理函数
function handleError(error) {
  const errorType = ERROR_MAPPING[error.code] || 'SYSTEM'
  const errorConfig = USER_ERRORS[errorType]

  return {
    message: errorConfig.message,
    action: errorConfig.action,
    icon: errorConfig.icon,
    canRetry: errorType === 'NETWORK' || errorType === 'SYSTEM',
  }
}
```

### 数据结构定义

#### 简化后的状态接口

```typescript
interface SimpleAiState {
  status: 'idle' | 'thinking' | 'executing' | 'responding'
  message: string
  progress?: number | null
  sessionId?: string | null
}

interface UserFriendlyError {
  message: string
  action: 'retry' | 'login' | 'rephrase'
  icon: string
  canRetry: boolean
}
```

### 用户交互流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant AI as AI服务

    U->>F: 输入任务描述
    F->>F: updateAiState('thinking', '正在理解您的需求...')
    F->>B: 发送消息
    B->>AI: 调用AI服务
    B->>F: SSE: processing_start
    F->>F: 保持thinking状态

    AI->>B: 需要调用工具
    B->>F: SSE: tool_execution_start
    F->>F: updateAiState('executing', '正在执行操作...')

    B->>B: 执行工具
    B->>F: SSE: tool_result_processing
    F->>F: updateAiState('responding', '正在生成回复...')

    AI->>B: 流式返回内容
    B->>F: SSE: chat_content_chunk
    F->>F: 显示流式内容

    B->>F: SSE: session_end
    F->>F: updateAiState('idle', '')
    F->>U: 显示完整回复
```

## ✅ 验收标准

### 技术指标

1. **代码复杂度降低**：

   - 前端 aiState 相关代码从 150+行减少到 50 行以内
   - SSE 消息处理逻辑从 10 个分支简化为 4 个状态处理

2. **响应速度提升**：

   - 状态更新响应时间 < 100ms
   - 错误处理响应时间 < 200ms

3. **错误率降低**：
   - 状态同步错误率 < 1%
   - 用户可理解错误提示覆盖率 > 95%

### 用户体验指标

1. **状态反馈清晰度**：

   - 用户能清楚理解当前 AI 处理阶段
   - 状态切换流畅，无卡顿感

2. **错误处理友好度**：

   - 错误提示用户友好，避免技术术语
   - 提供明确的解决建议和操作按钮

3. **操作简洁性**：
   - 用户操作步骤不增加
   - 界面元素保持简洁统一

## 🚨 风险评估

### 技术风险

1. **状态映射准确性风险**

   - **风险**：10→4 的状态映射可能丢失细节信息
   - **缓解**：保留原始 SSE 消息在开发模式下的日志记录

2. **向后兼容性风险**
   - **风险**：状态结构变更可能影响现有组件
   - **缓解**：采用渐进式重构，保留兼容接口

### 产品风险

1. **用户适应性风险**
   - **风险**：状态提示变更可能影响用户习惯
   - **缓解**：保持核心交互流程不变，仅优化提示文案

### 缓解策略

1. **分阶段实施**：

   - 第一阶段：后端状态映射层实现
   - 第二阶段：前端状态管理重构
   - 第三阶段：错误处理优化

2. **充分测试**：

   - 单元测试覆盖状态映射逻辑
   - 集成测试验证端到端状态流转
   - 用户测试验证体验改进效果

3. **回滚机制**：
   - 保留原有状态处理逻辑作为备用方案
   - 支持配置开关快速回滚

## 📅 开发计划

### 第一周：后端优化

- Day 1-2: 实现状态映射模块
- Day 3-4: 集成状态映射到 SSE 消息处理
- Day 5: 后端单元测试和集成测试

### 第二周：前端重构

- Day 1-2: 重构 aiState 状态管理
- Day 3-4: 实现错误处理优化
- Day 5: 前端组件适配和测试

### 验证和优化

- 端到端测试验证
- 性能测试和优化
- 用户体验测试和调整

## 🔧 详细接口设计

### 后端接口变更

#### 1. 新增状态映射模块

**文件路径**: `uniCloud-aliyun/cloudfunctions/ai/modules/state-mapper.js`

```javascript
/**
 * 状态映射配置
 */
const STATE_MAPPING = {
  processing_start: {
    state: 'THINKING',
    message: '正在理解您的需求...',
    stage: 'analyzing',
  },
  tool_call_start: {
    state: 'EXECUTING',
    message: '正在执行操作...',
    stage: 'calling',
  },
  tool_execution_start: {
    state: 'EXECUTING',
    message: '正在执行操作...',
    stage: 'executing',
  },
  tool_execution_complete: {
    state: 'EXECUTING',
    message: '操作执行完成，正在处理结果...',
    stage: 'processing',
  },
  tool_result_processing: {
    state: 'RESPONDING',
    message: '正在生成回复...',
    stage: 'generating',
  },
  chat_content_chunk: {
    state: 'RESPONDING',
    message: '正在回复...',
    stage: 'streaming',
  },
  session_end: {
    state: 'IDLE',
    message: '',
    stage: 'complete',
  },
  error: {
    state: 'IDLE',
    message: '',
    stage: 'error',
  },
}

/**
 * 错误码映射配置
 */
const ERROR_MAPPING = {
  NETWORK_ERROR: 'NETWORK',
  TIMEOUT: 'NETWORK',
  CONNECTION_FAILED: 'NETWORK',
  UNAUTHORIZED: 'AUTH',
  TOKEN_EXPIRED: 'AUTH',
  PARSE_ERROR: 'PARSE',
  INVALID_INPUT: 'PARSE',
  SYSTEM_ERROR: 'SYSTEM',
  SERVICE_UNAVAILABLE: 'SYSTEM',
  RATE_LIMIT: 'SYSTEM',
}

/**
 * 用户友好错误配置
 */
const USER_ERRORS = {
  NETWORK: {
    message: '网络连接不稳定，请检查网络后重试',
    action: 'retry',
    icon: 'wifi-off',
    canRetry: true,
    retryDelay: 3000,
  },
  AUTH: {
    message: '登录已过期，请重新登录',
    action: 'login',
    icon: 'lock',
    canRetry: false,
    retryDelay: 0,
  },
  PARSE: {
    message: '我没理解您的意思，能换个说法吗？',
    action: 'rephrase',
    icon: 'help-circle',
    canRetry: true,
    retryDelay: 0,
  },
  SYSTEM: {
    message: '系统暂时繁忙，请稍后重试',
    action: 'retry',
    icon: 'alert-circle',
    canRetry: true,
    retryDelay: 5000,
  },
}

/**
 * 将原始SSE消息映射为简化状态
 */
function mapToSimpleState(sseType, originalData) {
  const mapping = STATE_MAPPING[sseType]
  if (!mapping) {
    console.warn(`未知的SSE消息类型: ${sseType}`)
    return null
  }

  return {
    type: 'simple_state_update',
    timestamp: Date.now(),
    sessionId: originalData.sessionId,
    data: {
      state: mapping.state,
      message: mapping.message,
      stage: mapping.stage,
      progress: originalData.progress || null,
      // 保留原始数据用于调试
      _original:
        process.env.NODE_ENV === 'development'
          ? {
              type: sseType,
              data: originalData,
            }
          : undefined,
    },
  }
}

/**
 * 处理错误并返回用户友好的错误信息
 */
function mapError(error) {
  const errorCode = error.code || error.errCode || 'SYSTEM_ERROR'
  const errorType = ERROR_MAPPING[errorCode] || 'SYSTEM'
  const errorConfig = USER_ERRORS[errorType]

  return {
    type: 'user_friendly_error',
    timestamp: Date.now(),
    data: {
      message: errorConfig.message,
      action: errorConfig.action,
      icon: errorConfig.icon,
      canRetry: errorConfig.canRetry,
      retryDelay: errorConfig.retryDelay,
      // 保留原始错误用于调试
      _original:
        process.env.NODE_ENV === 'development'
          ? {
              code: errorCode,
              message: error.message,
              stack: error.stack,
            }
          : undefined,
    },
  }
}

module.exports = {
  mapToSimpleState,
  mapError,
  STATE_MAPPING,
  ERROR_MAPPING,
  USER_ERRORS,
}
```

#### 2. 修改主处理函数

**文件路径**: `uniCloud-aliyun/cloudfunctions/ai/index.obj.js`

需要在 SSE 消息发送前添加状态映射处理：

```javascript
const { mapToSimpleState, mapError } = require('./modules/state-mapper')

// 在现有的SSE消息发送函数中添加映射处理
const sendSSEMessage = async (sseChannel, type, sessionId, data) => {
  // 发送原始消息（保持向后兼容）
  await sseChannel.write(createSSEMessage(type, sessionId, data))

  // 发送简化状态消息
  const simplifiedMessage = mapToSimpleState(type, { ...data, sessionId })
  if (simplifiedMessage) {
    await sseChannel.write(simplifiedMessage)
  }
}

// 错误处理函数修改
const handleError = async (sseChannel, sessionId, error) => {
  // 发送用户友好的错误消息
  const userFriendlyError = mapError(error)
  await sseChannel.write(userFriendlyError)

  // 发送原始错误消息（用于调试）
  if (process.env.NODE_ENV === 'development') {
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.ERROR, sessionId, {
        error: error.message,
        timestamp: new Date().toISOString(),
      })
    )
  }
}
```

### 前端接口变更

#### 1. 简化状态管理

**文件路径**: `src/pages/aiAssistant/index.vue`

```javascript
// 简化后的状态定义
const SIMPLE_AI_STATES = {
  IDLE: 'idle',
  THINKING: 'thinking',
  EXECUTING: 'executing',
  RESPONDING: 'responding',
}

// 重构后的aiState
const aiState = ref({
  status: SIMPLE_AI_STATES.IDLE,
  message: '',
  stage: '',
  progress: null,
  sessionId: null,
  error: null,
})

// 状态更新函数
const updateAiState = (newState, message = '', stage = '', progress = null) => {
  aiState.value.status = newState
  aiState.value.message = message
  aiState.value.stage = stage
  aiState.value.progress = progress
  aiState.value.error = null
}

// 错误状态更新函数
const setErrorState = (errorInfo) => {
  aiState.value.status = SIMPLE_AI_STATES.IDLE
  aiState.value.message = ''
  aiState.value.stage = ''
  aiState.value.progress = null
  aiState.value.error = errorInfo
}

// 重置状态函数
const resetAiState = () => {
  aiState.value = {
    status: SIMPLE_AI_STATES.IDLE,
    message: '',
    stage: '',
    progress: null,
    sessionId: null,
    error: null,
  }
}
```

#### 2. 消息处理器重构

```javascript
// 简化的消息处理函数
const handleSSEMessage = (message) => {
  const { type, data, sessionId } = message

  // 验证会话ID
  if (aiState.value.sessionId && sessionId && sessionId !== aiState.value.sessionId) {
    console.warn('收到不匹配的会话消息，忽略', {
      expected: aiState.value.sessionId,
      received: sessionId,
    })
    return
  }

  switch (type) {
    case 'simple_state_update':
      handleSimpleStateUpdate(data, sessionId)
      break

    case 'user_friendly_error':
      handleUserFriendlyError(data)
      break

    // 保留原始消息处理用于向后兼容
    case 'chat_content_chunk':
      handleChatContentChunk(data)
      break

    default:
      console.warn('未知的消息类型：', type)
  }
}

// 简化状态更新处理
const handleSimpleStateUpdate = (data, sessionId) => {
  if (!aiState.value.sessionId) {
    aiState.value.sessionId = sessionId
  }

  updateAiState(data.state, data.message, data.stage, data.progress)

  // 根据状态更新UI
  switch (data.state) {
    case SIMPLE_AI_STATES.THINKING:
      // 显示思考状态
      break
    case SIMPLE_AI_STATES.EXECUTING:
      // 显示执行状态
      break
    case SIMPLE_AI_STATES.RESPONDING:
      // 准备接收流式内容
      if (data.stage === 'streaming') {
        createStreamingMessage()
      }
      break
    case SIMPLE_AI_STATES.IDLE:
      // 重置到空闲状态
      if (data.stage === 'complete') {
        finalizeStreamingMessage()
      }
      resetAiState()
      break
  }
}

// 用户友好错误处理
const handleUserFriendlyError = (errorData) => {
  setErrorState({
    message: errorData.message,
    action: errorData.action,
    icon: errorData.icon,
    canRetry: errorData.canRetry,
    retryDelay: errorData.retryDelay,
  })

  // 显示错误提示
  showErrorToast(errorData)

  // 如果支持自动重试
  if (errorData.canRetry && errorData.retryDelay > 0) {
    setTimeout(() => {
      showRetryOption()
    }, errorData.retryDelay)
  }
}
```

## 🎯 成功指标

### 量化指标

- 代码复杂度降低 > 50%
- 状态更新响应时间 < 100ms
- 错误处理覆盖率 > 95%
- 用户满意度提升至 4.5 分以上

### 定性指标

- 开发调试效率显著提升
- 用户反馈错误提示更友好
- 系统稳定性和可维护性增强

## 🔄 迁移策略和回滚方案

### 迁移策略

#### 阶段 1：后端兼容性改造（第 1-2 天）

1. **新增状态映射模块**

   - 创建 `state-mapper.js` 模块
   - 实现状态映射和错误处理函数
   - 添加单元测试

2. **修改 SSE 消息发送逻辑**

   - 保持原有消息格式不变（向后兼容）
   - 同时发送新的简化状态消息
   - 添加开发环境调试信息

3. **配置开关机制**
   ```javascript
   // 添加功能开关
   const FEATURE_FLAGS = {
     SIMPLIFIED_STATE: process.env.ENABLE_SIMPLIFIED_STATE === 'true',
     USER_FRIENDLY_ERRORS: process.env.ENABLE_USER_FRIENDLY_ERRORS === 'true',
   }
   ```

#### 阶段 2：前端渐进式重构（第 3-4 天）

1. **保留原有状态管理**

   - 不删除现有 `aiState` 结构
   - 新增简化状态管理并行运行
   - 通过配置开关控制使用哪套状态管理

2. **消息处理器扩展**

   - 扩展现有消息处理函数
   - 优先处理新的简化消息
   - 保留原有消息处理作为备用

3. **UI 组件适配**
   - 修改状态显示组件支持新的状态结构
   - 保持界面样式和交互不变
   - 添加错误提示组件

#### 阶段 3：测试和优化（第 5 天）

1. **功能测试**

   - 端到端测试验证状态流转
   - 错误场景测试
   - 性能测试

2. **用户体验测试**
   - 内部用户测试
   - 收集反馈和优化建议

### 回滚方案

#### 快速回滚机制

1. **环境变量控制**

   ```bash
   # 禁用新功能，回到原有实现
   ENABLE_SIMPLIFIED_STATE=false
   ENABLE_USER_FRIENDLY_ERRORS=false
   ```

2. **代码级回滚**

   - 保留原有代码分支
   - 通过配置开关快速切换
   - 数据库无需回滚（状态管理不涉及持久化）

3. **监控和告警**
   - 监控错误率和响应时间
   - 设置告警阈值
   - 自动回滚触发条件

#### 回滚触发条件

1. **技术指标异常**

   - 错误率超过 5%
   - 响应时间超过 500ms
   - 状态同步失败率超过 2%

2. **用户体验指标下降**
   - 用户投诉增加
   - 任务创建成功率下降
   - 用户满意度评分下降

## 📋 详细实施步骤

### Day 1: 后端状态映射模块开发

#### 上午任务

1. **创建状态映射模块**

   ```bash
   # 创建新文件
   touch uniCloud-aliyun/cloudfunctions/ai/modules/state-mapper.js
   ```

2. **实现核心映射函数**
   - 实现 `mapToSimpleState` 函数
   - 实现 `mapError` 函数
   - 添加配置常量

#### 下午任务

1. **编写单元测试**

   ```bash
   # 创建测试文件
   touch uniCloud-aliyun/cloudfunctions/ai/test/state-mapper.test.js
   ```

2. **测试用例覆盖**
   - 所有 SSE 消息类型的映射测试
   - 错误处理映射测试
   - 边界条件测试

### Day 2: 后端集成和配置

#### 上午任务

1. **修改主处理函数**

   - 在 `index.obj.js` 中集成状态映射
   - 添加 `sendSSEMessage` 包装函数
   - 修改错误处理逻辑

2. **添加功能开关**
   - 环境变量配置
   - 运行时开关控制

#### 下午任务

1. **集成测试**
   - 端到端消息流测试
   - 错误场景测试
   - 性能基准测试

### Day 3: 前端状态管理重构

#### 上午任务

1. **创建简化状态管理**

   - 定义新的状态结构
   - 实现状态更新函数
   - 保留原有状态管理

2. **扩展消息处理器**
   - 添加新消息类型处理
   - 保持向后兼容

#### 下午任务

1. **UI 组件适配**
   - 修改状态显示组件
   - 添加错误提示组件
   - 保持界面一致性

### Day 4: 前端错误处理优化

#### 上午任务

1. **实现用户友好错误处理**

   - 错误分类和显示
   - 重试机制
   - 用户引导

2. **错误提示组件**
   - 设计错误提示界面
   - 实现操作按钮
   - 添加图标和动画

#### 下午任务

1. **集成测试**
   - 前端状态流转测试
   - 错误处理测试
   - 用户交互测试

### Day 5: 测试验证和优化

#### 上午任务

1. **端到端测试**

   - 完整用户流程测试
   - 各种错误场景测试
   - 性能和稳定性测试

2. **用户体验测试**
   - 内部用户测试
   - 收集使用反馈

#### 下午任务

1. **优化和调整**

   - 根据测试结果优化
   - 修复发现的问题
   - 性能调优

2. **文档更新**
   - 更新技术文档
   - 更新用户手册
   - 准备发布说明

## 🔍 测试用例设计

### 状态映射测试

```javascript
describe('状态映射测试', () => {
  test('processing_start 映射为 THINKING 状态', () => {
    const result = mapToSimpleState('processing_start', { sessionId: 'test' })
    expect(result.data.state).toBe('THINKING')
    expect(result.data.message).toBe('正在理解您的需求...')
  })

  test('tool_execution_start 映射为 EXECUTING 状态', () => {
    const result = mapToSimpleState('tool_execution_start', { sessionId: 'test' })
    expect(result.data.state).toBe('EXECUTING')
    expect(result.data.message).toBe('正在执行操作...')
  })

  test('未知消息类型返回 null', () => {
    const result = mapToSimpleState('unknown_type', { sessionId: 'test' })
    expect(result).toBeNull()
  })
})
```

### 错误处理测试

```javascript
describe('错误处理测试', () => {
  test('网络错误映射为用户友好提示', () => {
    const error = { code: 'NETWORK_ERROR', message: 'Connection failed' }
    const result = mapError(error)
    expect(result.data.message).toBe('网络连接不稳定，请检查网络后重试')
    expect(result.data.canRetry).toBe(true)
  })

  test('认证错误映射为登录提示', () => {
    const error = { code: 'UNAUTHORIZED', message: 'Token expired' }
    const result = mapError(error)
    expect(result.data.message).toBe('登录已过期，请重新登录')
    expect(result.data.action).toBe('login')
  })
})
```

### 前端状态管理测试

```javascript
describe('前端状态管理测试', () => {
  test('状态更新函数正确更新状态', () => {
    updateAiState('thinking', '正在思考...', 'analyzing', 50)
    expect(aiState.value.status).toBe('thinking')
    expect(aiState.value.message).toBe('正在思考...')
    expect(aiState.value.progress).toBe(50)
  })

  test('错误状态设置正确', () => {
    const errorInfo = {
      message: '网络错误',
      action: 'retry',
      canRetry: true,
    }
    setErrorState(errorInfo)
    expect(aiState.value.status).toBe('idle')
    expect(aiState.value.error).toEqual(errorInfo)
  })
})
```
