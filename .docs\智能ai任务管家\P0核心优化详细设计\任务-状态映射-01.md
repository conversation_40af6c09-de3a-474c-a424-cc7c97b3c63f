# 任务-状态映射-01：创建状态映射模块文件

## 任务概述

**任务ID**: 任务-状态映射-01  
**所属功能模块**: 后端状态映射模块开发  
**优先级**: P0（高优先级）  
**预估工时**: 4小时  
**负责人**: 后端开发工程师  

## 任务描述

在uniCloud-aliyun/cloudfunctions/ai/modules/目录下创建state-mapper.js文件，定义状态映射配置和错误映射配置，实现基础模块结构。这是整个状态映射功能的基础文件，为后续的状态映射和错误处理功能提供配置支持。

## 技术实现详情

### 1. 文件创建
- **文件路径**: `uniCloud-aliyun/cloudfunctions/ai/modules/state-mapper.js`
- **文件作用**: 状态映射和错误处理的核心配置模块

### 2. 状态映射配置 (STATE_MAPPING)

根据需求文档，需要将10种SSE消息类型映射为4种简化状态：

```javascript
const STATE_MAPPING = {
  // 思考阶段
  processing_start: {
    state: 'THINKING',
    message: '正在理解您的需求...',
    stage: 'analyzing',
  },
  
  // 执行阶段
  tool_call_start: {
    state: 'EXECUTING',
    message: '正在执行操作...',
    stage: 'calling',
  },
  tool_execution_start: {
    state: 'EXECUTING',
    message: '正在执行操作...',
    stage: 'executing',
  },
  tool_execution_complete: {
    state: 'EXECUTING',
    message: '操作执行完成，正在处理结果...',
    stage: 'processing',
  },
  
  // 回复阶段
  tool_result_processing: {
    state: 'RESPONDING',
    message: '正在生成回复...',
    stage: 'generating',
  },
  chat_content_chunk: {
    state: 'RESPONDING',
    message: '正在回复...',
    stage: 'streaming',
  },
  
  // 空闲阶段
  session_end: {
    state: 'IDLE',
    message: '',
    stage: 'complete',
  },
  error: {
    state: 'IDLE',
    message: '',
    stage: 'error',
  },
}
```

### 3. 错误映射配置 (ERROR_MAPPING)

将技术错误码映射为用户友好的错误类型：

```javascript
const ERROR_MAPPING = {
  // 网络相关错误
  NETWORK_ERROR: 'NETWORK',
  TIMEOUT: 'NETWORK',
  CONNECTION_FAILED: 'NETWORK',
  
  // 认证相关错误
  UNAUTHORIZED: 'AUTH',
  TOKEN_EXPIRED: 'AUTH',
  
  // 解析相关错误
  PARSE_ERROR: 'PARSE',
  INVALID_INPUT: 'PARSE',
  
  // 系统相关错误
  SYSTEM_ERROR: 'SYSTEM',
  SERVICE_UNAVAILABLE: 'SYSTEM',
  RATE_LIMIT: 'SYSTEM',
}
```

### 4. 用户友好错误配置 (USER_ERRORS)

定义用户友好的错误提示和操作建议：

```javascript
const USER_ERRORS = {
  NETWORK: {
    message: '网络连接不稳定，请检查网络后重试',
    action: 'retry',
    icon: 'wifi-off',
    canRetry: true,
    retryDelay: 3000,
  },
  AUTH: {
    message: '登录已过期，请重新登录',
    action: 'login',
    icon: 'lock',
    canRetry: false,
    retryDelay: 0,
  },
  PARSE: {
    message: '我没理解您的意思，能换个说法吗？',
    action: 'rephrase',
    icon: 'help-circle',
    canRetry: true,
    retryDelay: 0,
  },
  SYSTEM: {
    message: '系统暂时繁忙，请稍后重试',
    action: 'retry',
    icon: 'alert-circle',
    canRetry: true,
    retryDelay: 5000,
  },
}
```

### 5. 模块导出结构

```javascript
module.exports = {
  STATE_MAPPING,
  ERROR_MAPPING,
  USER_ERRORS,
  // 后续任务中将添加的函数
  // mapToSimpleState,
  // mapError,
}
```

## 验收标准

### 功能验收
- [ ] state-mapper.js文件创建成功
- [ ] STATE_MAPPING配置包含所有10种SSE消息类型
- [ ] ERROR_MAPPING配置覆盖主要错误类型
- [ ] USER_ERRORS配置提供用户友好的错误信息
- [ ] 模块导出结构正确

### 代码质量验收
- [ ] 代码符合项目编码规范
- [ ] 配置结构清晰，易于维护
- [ ] 注释完整，说明配置用途
- [ ] 文件大小合理（<5KB）

### 技术规范验收
- [ ] 遵循项目开发规范中的命名规范
- [ ] 使用kebab-case文件命名
- [ ] 配置常量使用UPPER_CASE命名
- [ ] 模块导出使用CommonJS规范

## 依赖关系

### 前置依赖
- 无（这是第一个任务）

### 后续依赖
- 任务-状态映射-02：实现状态映射函数（依赖本任务的配置）
- 任务-状态映射-03：实现错误处理映射函数（依赖本任务的配置）

## 实施步骤

### Step 1: 创建文件结构（30分钟）
1. 在modules目录下创建state-mapper.js文件
2. 添加文件头部注释和模块说明
3. 定义基础的模块结构

### Step 2: 实现状态映射配置（1.5小时）
1. 根据需求文档定义STATE_MAPPING配置
2. 确保覆盖所有10种SSE消息类型
3. 为每个状态添加合适的用户提示文案

### Step 3: 实现错误映射配置（1.5小时）
1. 定义ERROR_MAPPING技术错误到用户错误的映射
2. 实现USER_ERRORS用户友好错误配置
3. 为每种错误类型设计合适的操作建议

### Step 4: 完善模块导出（30分钟）
1. 设置正确的模块导出结构
2. 添加必要的JSDoc注释
3. 进行代码格式化和检查

### Step 5: 自测验证（30分钟）
1. 检查配置完整性
2. 验证导出结构正确性
3. 确保符合编码规范

## 注意事项

### 开发注意事项
1. **配置完整性**: 确保STATE_MAPPING覆盖所有现有的SSE消息类型
2. **用户体验**: USER_ERRORS中的错误提示要简洁明了，避免技术术语
3. **扩展性**: 配置结构要便于后续扩展新的状态和错误类型
4. **一致性**: 错误提示的语言风格要保持一致

### 技术注意事项
1. **性能考虑**: 配置对象要避免过深的嵌套结构
2. **内存优化**: 使用常量定义避免重复创建对象
3. **类型安全**: 虽然是JavaScript，但要保持数据类型的一致性
4. **调试支持**: 为开发模式预留调试信息的配置空间

### 兼容性注意事项
1. **向后兼容**: 新的状态映射不能影响现有的SSE消息处理
2. **渐进升级**: 支持新旧状态管理机制的并存
3. **回滚准备**: 配置要支持快速回滚到原有实现

## 测试建议

虽然这个任务主要是配置文件创建，但建议进行以下验证：

1. **配置完整性测试**: 验证所有必需的配置项都已定义
2. **数据结构测试**: 确保配置对象的结构符合预期
3. **导入测试**: 在其他模块中测试导入功能是否正常
4. **类型检查**: 验证配置值的数据类型正确性

## 风险评估

### 低风险
- 这是一个纯配置文件，不涉及复杂逻辑
- 不会影响现有功能的正常运行

### 潜在风险
- 配置遗漏可能导致后续功能开发受阻
- 错误提示文案不当可能影响用户体验

### 风险缓解
- 仔细对照需求文档检查配置完整性
- 与产品经理确认错误提示文案的合适性
