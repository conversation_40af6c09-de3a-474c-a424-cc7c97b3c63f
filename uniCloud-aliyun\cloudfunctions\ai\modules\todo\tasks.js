/**
 * Todo 任务管理模块
 * 负责任务的 CRUD 操作和查询功能
 */

const { API_CONFIG, TASK_CONFIG, ERROR_CODES } = require('./config')
const {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  formatDateForApi,
  simplifyTaskData,
  removeEmptyFields,
} = require('./utils')

/**
 * 任务管理类
 */
class TaskManager {
  constructor(authManager) {
    this.authManager = authManager
  }

  /**
   * 获取任务列表
   * @param {object} options - 查询参数
   * @returns {object} 任务列表
   */
  async getTasks(options = {}) {
    const {
      mode = 'all',
      keyword = null,
      priority = null,
      projectName = null,
      completed = null,
      limit = null,
      offset = 0,
    } = options

    console.log('[TaskManager.getTasks] 参数', { mode, keyword, priority, projectName, completed, limit, offset })

    try {
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TaskManager.getTasks] 错误：', batchResult.errMsg)
        return batchResult
      }

      const { tasks, projects, tags } = batchResult.data
      console.log('[TaskManager.getTasks] 基础数据', {
        tasksCount: tasks.length,
        projectsCount: projects.length,
        tasks,
        projects,
        tagsCount: Array.isArray(tags) ? tags.length : 0,
      })
      let filteredTasks = []

      // === 额外补充：逐清单拉取「已完成任务」并与基础列表合并 ===
      const completedTaskMap = new Map()
      try {
        if (Array.isArray(projects) && projects.length > 0) {
          const completedResults = await Promise.all(
            projects.map(async (p) => {
              try {
                const url = `${API_CONFIG.PROJECT_URL}/${p.id}/completed/`
                const res = await this.authManager._request('GET', url)
                if (res && !res.errCode && Array.isArray(res.data)) {
                  return res.data
                }
              } catch (e) {
                console.warn('[TaskManager.getTasks] 获取清单已完成任务失败，跳过', { projectId: p.id, error: e && e.message })
              }
              return []
            })
          )

          // 扁平化并填充关键字段后放入 Map
          for (const completedList of completedResults) {
            for (const ct of completedList) {
              const normalized = { ...ct }
              normalized.status = TASK_CONFIG.STATUS.COMPLETED
              normalized.isCompleted = true
              if (!normalized.completedTime) {
                normalized.completedTime = normalized.modifiedTime || null
              }
              if (!normalized.completedUserId) {
                normalized.completedUserId = normalized.creator
              }
              const key = normalized.id || `${normalized.creator || ''}_${normalized.title || ''}`
              completedTaskMap.set(key, normalized)
            }
          }
        }
      } catch (mergeError) {
        console.warn('[TaskManager.getTasks] 合并已完成任务失败（继续执行）', mergeError && mergeError.message)
      }

      // 依据基础 tasks 和 completedTaskMap 生成 allTasks
      const allTasks = []
      const seenIds = new Set()
      for (const task of tasks) {
        // 只处理文本类型的任务
        if (task.kind !== TASK_CONFIG.KIND.TEXT) {
          console.debug('[TaskManager.getTasks] 跳过任务：非 TEXT 类型', { id: task.id, kind: task.kind })
          continue
        }

        const keyById = task.id
        const keyByCreatorTitle = `${task.creator || ''}_${task.title || ''}`
        const completedHit = (keyById && completedTaskMap.get(keyById)) || completedTaskMap.get(keyByCreatorTitle)

        let merged = task
        if (completedHit) {
          // 保留原始关键字段
          const preserved = {
            id: task.id,
            projectId: task.projectId,
            columnId: task.columnId,
            sortOrder: task.sortOrder,
            tags: task.tags || [],
          }
          merged = { ...completedHit, ...preserved }
          merged.isCompleted = true
        } else {
          // 若基础列表标记为已完成但未在 completed 集合，按 Python 逻辑回滚为未完成
          if (merged.status === TASK_CONFIG.STATUS.COMPLETED) {
            merged.status = TASK_CONFIG.STATUS.ACTIVE
          }
          merged.isCompleted = false
        }

        allTasks.push(merged)
        if (merged.id) seenIds.add(merged.id)
      }

      // 添加仅存在于 completed 集合的任务
      for (const [key, ct] of completedTaskMap.entries()) {
        if (ct.id && seenIds.has(ct.id)) continue
        allTasks.push(ct)
        if (ct.id) seenIds.add(ct.id)
      }

      // 处理时间筛选（对齐 Python 逻辑，按中国时区日界判断；并处理 00:00:00 截止日顺延）
      const CHINA_OFFSET_MS = 8 * 60 * 60 * 1000
      const now = new Date()
      const chinaDayIndex = (d) => Math.floor((d.getTime() + CHINA_OFFSET_MS) / (24 * 60 * 60 * 1000))
      const todayIndex = chinaDayIndex(now)
      const yesterdayIndex = todayIndex - 1
      const sevenDaysAgoIndex = todayIndex - 7

      // "今天"模式下，未显式指定 completed 时，默认只显示未完成
      let effectiveCompleted = completed
      if (mode === TASK_CONFIG.FILTER_MODE.TODAY && effectiveCompleted === null) {
        effectiveCompleted = false
        console.debug('[TaskManager.getTasks] 今天模式，默认设置 completed=false')
      }

      const parseDate = (value) => {
        if (!value) return null
        const d = new Date(value)
        return isNaN(d.getTime()) ? null : d
      }

      const getDayIndex = (d) => (d ? chinaDayIndex(d) : null)
      const isMidnightString = (s) => typeof s === 'string' && s.includes('T') && /T00:00:00/.test(s)
      const addDaysIso = (s, days) => {
        try {
          const d = new Date(s)
          if (isNaN(d.getTime())) return s
          d.setUTCDate(d.getUTCDate() + days)
          return d.toISOString()
        } catch {
          return s
        }
      }

      for (const task of allTasks) {
        const baseInfo = {
          id: task.id,
          title: task.title,
          kind: task.kind,
          modifiedTime: task.modifiedTime,
          status: task.status,
          priority: task.priority,
          projectId: task.projectId,
        }
        console.debug('[TaskManager.getTasks] 检查任务', baseInfo)

        // 时间筛选：使用 dueDate 或 startDate 判断；若两者皆无，回退到 modifiedTime
        if (mode !== TASK_CONFIG.FILTER_MODE.ALL) {
          const startDate = parseDate(task.startDate)
          let dueDate = parseDate(task.dueDate)
          // 若 dueDate 为 00:00:00，展示筛选时按次日判定
          if (!dueDate && isMidnightString(task.dueDate)) {
            dueDate = parseDate(addDaysIso(task.dueDate, 1))
          }
          const taskDate = dueDate || startDate || (task.modifiedTime ? parseDate(task.modifiedTime) : null)

          if (!taskDate) {
            console.debug('[TaskManager.getTasks] 跳过任务：无可用日期字段进行时间筛选', { id: task.id, mode })
            continue
          }

          const taskDayIdx = getDayIndex(taskDate)
          console.debug('[TaskManager.getTasks] 时间筛选对比', {
            id: task.id,
            mode,
            taskDayIdx,
            todayIndex,
            yesterdayIndex,
            sevenDaysAgoIndex,
            startDate: startDate ? startDate.toISOString() : null,
            dueDate: dueDate ? dueDate.toISOString() : null,
          })

          if (mode === TASK_CONFIG.FILTER_MODE.TODAY) {
            let hit = false
            if (taskDayIdx === todayIndex) {
              hit = true
            }
            if (!hit && startDate) {
              const startIdx = getDayIndex(startDate)
              const dueIdx = dueDate ? getDayIndex(dueDate) : null
              if (startIdx < todayIndex && (dueIdx === null || dueIdx >= todayIndex)) {
                hit = true
              }
            }
            if (!hit) {
              console.debug('[TaskManager.getTasks] 跳过任务：今日模式未命中', { id: task.id })
              continue
            }
          }

          if (mode === TASK_CONFIG.FILTER_MODE.YESTERDAY) {
            if (taskDayIdx !== yesterdayIndex) {
              console.debug('[TaskManager.getTasks] 跳过任务：非昨日', { id: task.id })
              continue
            }
          }

          if (mode === TASK_CONFIG.FILTER_MODE.RECENT_7_DAYS) {
            let hit = false
            if (taskDayIdx >= sevenDaysAgoIndex) {
              hit = true
            }
            if (!hit && startDate && dueDate) {
              const startIdx = getDayIndex(startDate)
              const dueIdx = getDayIndex(dueDate)
              if (startIdx < sevenDaysAgoIndex && dueIdx >= sevenDaysAgoIndex) {
                hit = true
              }
            }
            if (!hit) {
              console.debug('[TaskManager.getTasks] 跳过任务：不在最近7天', { id: task.id })
              continue
            }
          }
        }

        // 完成状态筛选
        if (effectiveCompleted !== null) {
          const isCompleted = task.status === TASK_CONFIG.STATUS.COMPLETED || !!task.isCompleted
          if (effectiveCompleted !== isCompleted) {
            console.debug('[TaskManager.getTasks] 跳过任务：完成状态不匹配', {
              id: task.id,
              expect: effectiveCompleted,
              actual: isCompleted,
              status: task.status,
            })
            continue
          }
        }

        // 优先级筛选（宽松：支持字符串/数字）
        if (priority !== null) {
          const priorityFilter = Number(priority)
          if (!Number.isNaN(priorityFilter)) {
            if (Number(task.priority) !== priorityFilter) {
              console.debug('[TaskManager.getTasks] 跳过任务：优先级不匹配（数字）', {
                id: task.id,
                expect: priorityFilter,
                actual: Number(task.priority),
                raw: task.priority,
              })
              continue
            }
          } else {
            // 无法解析为数字时，回退为严格比较
            if (task.priority !== priority) {
              console.debug('[TaskManager.getTasks] 跳过任务：优先级不匹配（字符串）', {
                id: task.id,
                expect: priority,
                actual: task.priority,
              })
              continue
            }
          }
        }

        // 关键词筛选
        if (keyword) {
          const searchText = `${task.title || ''} ${task.content || ''}`.toLowerCase()
          const hit = searchText.includes(keyword.toLowerCase())
          console.debug('[TaskManager.getTasks] 关键词匹配', { id: task.id, keyword, hit })
          if (!hit) continue
        }

        // 清单名称筛选
        if (projectName) {
          const project = projects.find((p) => p.id === task.projectId)
          if (!project) {
            console.debug('[TaskManager.getTasks] 跳过任务：未找到所属清单', { id: task.id, projectId: task.projectId })
            continue
          }
          const nameHit = project.name.toLowerCase().includes(projectName.toLowerCase())
          console.debug('[TaskManager.getTasks] 清单名称匹配', {
            id: task.id,
            projectName: project.name,
            filter: projectName,
            hit: nameHit,
          })
          if (!nameHit) continue
        }

        // 简化任务数据
        const simplifiedTask = simplifyTaskData(task)

        // 添加清单信息
        if (task.projectId) {
          const project = projects.find((p) => p.id === task.projectId)
          if (project) {
            simplifiedTask.projectName = project.name
            simplifiedTask.projectColor = project.color
          }
        }

        // 添加标签详情（基于批量 tags 数据）
        if (Array.isArray(tags) && Array.isArray(task.tags) && task.tags.length > 0) {
          const tagDetails = []
          for (const tagId of task.tags) {
            const t = tags.find((tg) => tg.id === tagId)
            if (t) {
              tagDetails.push({ id: t.id, name: t.name, label: t.label || t.name })
            }
          }
          if (tagDetails.length > 0) simplifiedTask.tagDetails = tagDetails
        }

        filteredTasks.push(simplifiedTask)
        console.debug('[TaskManager.getTasks] 通过筛选，已添加', {
          id: simplifiedTask.id || task.id,
          title: simplifiedTask.title || task.title,
          projectName: simplifiedTask.projectName,
          priority: simplifiedTask.priority,
        })
      }

      // 分页/裁剪（最佳实践：由工具层处理）
      console.log('[TaskManager.getTasks] 过滤后数量', { filteredCount: filteredTasks.length })
      let slicedTasks = filteredTasks
      const normalizedOffset = Number.isInteger(offset) && offset > 0 ? offset : 0
      if (Number.isInteger(limit) && limit > 0) {
        slicedTasks = filteredTasks.slice(normalizedOffset, normalizedOffset + limit)
      } else if (normalizedOffset > 0) {
        slicedTasks = filteredTasks.slice(normalizedOffset)
      }

      const result = createSuccessResponse('获取任务列表成功', slicedTasks)
      console.log('[TaskManager.getTasks] 返回结果', { count: slicedTasks.length, preview: slicedTasks.slice(0, 2) })
      return result
    } catch (error) {
      console.error('[TaskManager.getTasks] 错误：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取任务列表失败', error)
    }
  }

  /**
   * 创建任务
   * @param {object} options - 任务数据
   * @returns {object} 创建结果
   */
  async createTask(options = {}) {
    // 使用安全的参数处理方式，类似 Python 版本
    const {
      title = null,
      content = null,
      priority = null,
      projectName = null,
      tagNames = null,
      startDate = null,
      dueDate = null,
      isAllDay = null,
      reminder = null,
      kind = null,
    } = options

    // 参数校验
    const validation = validateParams({ title }, ['title'])
    if (validation) {
      console.warn('[TaskManager.createTask] 参数校验失败：', validation.errMsg)
      return validation
    }

    try {
      // 获取基础数据以查找清单和标签
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TaskManager.createTask] 错误：', batchResult.errMsg)
        return batchResult
      }

      const { projects, tags } = batchResult.data
      let projectId = null
      let tagIds = []

      // 查找清单 ID（如果指定了清单名称）：先精确（忽略大小写），再模糊包含匹配
      if (projectName !== null) {
        if (projectName) {
          const lower = projectName.toLowerCase()
          let project = projects.find((p) => (p.name || '').toLowerCase() === lower)
          if (!project) {
            project = projects.find(
              (p) => (p.name || '').toLowerCase().includes(lower) || lower.includes((p.name || '').toLowerCase())
            )
          }
          if (project) {
            projectId = project.id
          } else {
            console.error('[TaskManager.createTask] 错误：', `未找到清单：${projectName}`)
            return createErrorResponse(ERROR_CODES.PROJECT_NOT_FOUND, `未找到清单：${projectName}`)
          }
        }
      }

      // 查找标签 ID（如果指定了标签名称）
      if (tagNames !== null) {
        if (tagNames && Array.isArray(tagNames) && tagNames.length > 0) {
          for (const tagName of tagNames) {
            const tag = tags.find((t) => t.name.toLowerCase() === tagName.toLowerCase())
            if (tag) {
              tagIds.push(tag.id)
            } else {
              console.warn('[TaskManager.createTask] 警告：', `未找到标签：${tagName}`)
            }
          }
        }
      }

      // 使用安全的参数处理方式，类似 Python 版本
      const safeTaskData = {
        title: title,
        content: content !== null ? content : '',
        priority: priority !== null ? priority : 0,
        kind: kind !== null ? kind : 'TEXT',
        status: TASK_CONFIG.STATUS.ACTIVE,
        isAllDay: isAllDay !== null ? isAllDay : true,
      }

      // 添加可选字段（只有在明确指定时才添加）
      if (projectId !== null) safeTaskData.projectId = projectId
      if (tagIds.length > 0) safeTaskData.tags = tagIds

      // 处理开始日期
      if (startDate !== null) {
        const formattedStartDate = startDate ? formatDateForApi(startDate) : null
        safeTaskData.startDate = formattedStartDate
      }

      // 处理截止日期
      if (dueDate !== null) {
        const formattedDueDate = dueDate ? formatDateForApi(dueDate) : null
        safeTaskData.dueDate = formattedDueDate
      }

      if (reminder !== null) safeTaskData.reminder = reminder

      // 移除空值字段
      const cleanTaskData = removeEmptyFields(safeTaskData)

      // 发送创建请求
      const result = await this.authManager._request('POST', API_CONFIG.TASK_URL, cleanTaskData)

      if (result.errCode) {
        console.error('[TaskManager.createTask] 错误：', result.errMsg)
        return result
      }

      // 统一返回结构：返回简化后的任务（与 getTasks 一致）
      const simplified = simplifyTaskData(result.data || {})
      if (simplified.projectId) {
        const project = projects.find((p) => p.id === simplified.projectId)
        if (project) {
          simplified.projectName = project.name
          simplified.projectColor = project.color
        }
      }
      // 如含标签，补充 tagDetails
      if (Array.isArray(tags) && Array.isArray(simplified.tags) && simplified.tags.length > 0) {
        const tagDetails = []
        for (const tagId of simplified.tags) {
          const t = tags.find((tg) => tg.id === tagId)
          if (t) tagDetails.push({ id: t.id, name: t.name, label: t.label || t.name })
        }
        if (tagDetails.length > 0) simplified.tagDetails = tagDetails
      }

      const finalResult = createSuccessResponse('任务创建成功', simplified)

      return finalResult
    } catch (error) {
      console.error('[TaskManager.createTask] 错误：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '创建任务失败', error)
    }
  }

  /**
   * 获取单个任务详情
   * @param {string} taskId - 任务 ID
   * @returns {object} 任务详情
   */
  async getTask(taskId) {
    // 参数校验
    const validation = validateParams({ taskId }, ['taskId'])
    if (validation) {
      console.warn('[TaskManager.getTask] 参数校验失败：', validation.errMsg)
      return validation
    }

    try {
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TaskManager.getTask] 错误：', batchResult.errMsg)
        return batchResult
      }

      const { tasks } = batchResult.data
      let task = tasks.find((t) => t.id === taskId)
      // 按标题定位兜底
      if (!task) {
        task = tasks.find((t) => (t.title || '') === taskId)
      }

      if (!task) {
        console.warn('[TaskManager.getTask] 警告：', `未找到任务：${taskId}`)
        return createErrorResponse(ERROR_CODES.TASK_NOT_FOUND, '任务不存在')
      }

      // 简化任务数据
      const simplifiedTask = simplifyTaskData(task)

      const result = createSuccessResponse('获取任务详情成功', simplifiedTask)

      return result
    } catch (error) {
      console.error('[TaskManager.getTask] 错误：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取任务详情失败', error)
    }
  }

  /**
   * 更新任务
   * @param {string} taskId - 任务 ID
   * @param {object} updateData - 更新数据
   * @returns {object} 更新结果
   */
  async updateTask(taskIdOrTitle, updateData) {
    // 参数校验
    const validation = validateParams({ taskId: taskIdOrTitle }, ['taskId'])
    if (validation) {
      console.warn('[TaskManager.updateTask] 参数校验失败：', validation.errMsg)
      return validation
    }

    try {
      // 首先获取现有任务数据
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TaskManager.updateTask] 错误：', batchResult.errMsg)
        return batchResult
      }

      const { tasks, projects, tags } = batchResult.data

      // 查找现有任务
      let existingTask = tasks.find((t) => t.id === taskIdOrTitle)
      if (!existingTask) {
        existingTask = tasks.find((t) => (t.title || '') === taskIdOrTitle)
      }
      if (!existingTask) {
        console.error('[TaskManager.updateTask] 错误：', `未找到任务：${taskIdOrTitle}`)
        return createErrorResponse(ERROR_CODES.TASK_NOT_FOUND, `未找到任务：${taskIdOrTitle}`)
      }

      // 处理清单名称转换为清单 ID
      let projectId = updateData.projectId
      if (updateData.projectName !== undefined) {
        if (updateData.projectName) {
          const project = projects.find((p) => p.name.toLowerCase() === updateData.projectName.toLowerCase())
          if (project) {
            projectId = project.id
          } else {
            console.error('[TaskManager.updateTask] 错误：', `未找到清单：${updateData.projectName}`)
            return createErrorResponse(ERROR_CODES.PROJECT_NOT_FOUND, `未找到清单：${updateData.projectName}`)
          }
        } else {
          projectId = null
        }
      }

      // 处理标签名称转换为标签 ID
      let tagIds = updateData.tags
      if (updateData.tagNames !== undefined) {
        if (updateData.tagNames && Array.isArray(updateData.tagNames)) {
          tagIds = []
          for (const tagName of updateData.tagNames) {
            const tag = tags.find((t) => t.name.toLowerCase() === tagName.toLowerCase())
            if (tag) {
              tagIds.push(tag.id)
            } else {
              console.warn('[TaskManager.updateTask] 警告：', `未找到标签：${tagName}`)
            }
          }
        } else {
          tagIds = []
        }
      }

      // completed 语义映射到 status（最佳实践：由工具层处理）
      let mappedStatus = updateData.status
      if (updateData.completed !== undefined) {
        mappedStatus = updateData.completed ? TASK_CONFIG.STATUS.COMPLETED : TASK_CONFIG.STATUS.ACTIVE
      }

      // 使用安全的参数处理方式，类似 Python 版本
      const safeUpdateData = {
        id: existingTask.id,
        title: updateData.title !== undefined ? updateData.title : existingTask.title,
        content: updateData.content !== undefined ? updateData.content : existingTask.content,
        priority: updateData.priority !== undefined ? updateData.priority : existingTask.priority,
        projectId: projectId !== undefined ? projectId : existingTask.projectId,
        tags: tagIds !== undefined ? tagIds : existingTask.tags || [],
        isAllDay: updateData.isAllDay !== undefined ? updateData.isAllDay : existingTask.isAllDay,
        status: mappedStatus !== undefined ? mappedStatus : existingTask.status,
        kind: updateData.kind !== undefined ? updateData.kind : existingTask.kind,
      }

      // 处理日期字段
      if (updateData.startDate !== undefined) {
        safeUpdateData.startDate = updateData.startDate ? formatDateForApi(updateData.startDate) : null
      } else if (existingTask.startDate) {
        safeUpdateData.startDate = existingTask.startDate
      }

      if (updateData.dueDate !== undefined) {
        safeUpdateData.dueDate = updateData.dueDate ? formatDateForApi(updateData.dueDate) : null
      } else if (existingTask.dueDate) {
        safeUpdateData.dueDate = existingTask.dueDate
      }

      // 处理提醒字段
      if (updateData.reminder !== undefined) {
        safeUpdateData.reminder = updateData.reminder
      } else if (existingTask.reminder) {
        safeUpdateData.reminder = existingTask.reminder
      }

      // 处理完成时间字段
      if (updateData.completedTime !== undefined) {
        safeUpdateData.completedTime = updateData.completedTime
      } else if (existingTask.completedTime) {
        safeUpdateData.completedTime = existingTask.completedTime
      }

      // 移除空值字段
      const cleanUpdateData = removeEmptyFields(safeUpdateData)

      // 发送更新请求
      const result = await this.authManager._request('POST', `${API_CONFIG.TASK_URL}/${existingTask.id}`, cleanUpdateData)
      if (result.errCode) {
        console.error('[TaskManager.updateTask] 错误：', result.errMsg)
        return result
      }

      const finalResult = createSuccessResponse('任务更新成功', result.data)

      return finalResult
    } catch (error) {
      console.error('[TaskManager.updateTask] 错误：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '更新任务失败', error)
    }
  }

  /**
   * 删除任务
   * @param {string} taskId - 任务 ID
   * @returns {object} 删除结果
   */
  async deleteTask(taskIdOrTitle) {
    // 参数校验
    const validation = validateParams({ taskId: taskIdOrTitle }, ['taskId'])
    if (validation) {
      console.warn('[TaskManager.deleteTask] 参数校验失败：', validation.errMsg)
      return validation
    }

    try {
      // 若传入标题，先解析为真实 ID
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TaskManager.deleteTask] 错误：', batchResult.errMsg)
        return batchResult
      }
      const { tasks } = batchResult.data
      let target = tasks.find((t) => t.id === taskIdOrTitle)
      if (!target) target = tasks.find((t) => (t.title || '') === taskIdOrTitle)
      if (!target) {
        console.warn('[TaskManager.deleteTask] 警告：', `未找到任务：${taskIdOrTitle}`)
        return createErrorResponse(ERROR_CODES.TASK_NOT_FOUND, `未找到任务：${taskIdOrTitle}`)
      }

      // 使用批量接口删除（与 Python 版一致，需要携带 projectId）
      const deletePayload = {
        add: [],
        addAttachments: [],
        delete: [
          {
            taskId: target.id,
            projectId: target.projectId,
          },
        ],
        deleteAttachments: [],
        update: [],
      }

      const result = await this.authManager._request('POST', API_CONFIG.BATCH_TASK_URL, deletePayload)
      if (result.errCode) {
        console.error('[TaskManager.deleteTask] 错误：', result.errMsg)
        return result
      }

      const finalResult = createSuccessResponse('任务删除成功', { id: target.id, title: target.title, projectId: target.projectId })

      return finalResult
    } catch (error) {
      console.error('[TaskManager.deleteTask] 错误：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '删除任务失败', error)
    }
  }
}

module.exports = TaskManager
