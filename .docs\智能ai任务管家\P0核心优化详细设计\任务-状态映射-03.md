# 任务-状态映射-03：实现错误处理映射函数

## 任务概述

**任务ID**: 任务-状态映射-03  
**所属功能模块**: 后端状态映射模块开发  
**优先级**: P0（高优先级）  
**预估工时**: 5小时  
**负责人**: 后端开发工程师  

## 任务描述

实现mapError函数，将技术错误映射为用户友好的错误信息（NETWORK/AUTH/PARSE/SYSTEM）。支持重试机制和操作建议，为用户提供清晰的错误反馈和解决方案。

## 技术实现详情

### 1. 核心函数实现

在state-mapper.js中实现mapError函数：

```javascript
/**
 * 处理错误并返回用户友好的错误信息
 * @param {Error|Object} error - 错误对象
 * @returns {Object} - 用户友好的错误消息对象
 */
function mapError(error) {
  const errorCode = error.code || error.errCode || 'SYSTEM_ERROR'
  const errorType = ERROR_MAPPING[errorCode] || 'SYSTEM'
  const errorConfig = USER_ERRORS[errorType]

  return {
    type: 'user_friendly_error',
    timestamp: Date.now(),
    data: {
      message: errorConfig.message,
      action: errorConfig.action,
      icon: errorConfig.icon,
      canRetry: errorConfig.canRetry,
      retryDelay: errorConfig.retryDelay,
      errorType: errorType,
      // 保留原始错误用于调试
      _original: process.env.NODE_ENV === 'development' ? {
        code: errorCode,
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      } : undefined,
    },
  }
}
```

### 2. 错误分类处理

#### 2.1 网络错误 (NETWORK)
```javascript
// 处理网络相关错误
const networkErrors = ['NETWORK_ERROR', 'TIMEOUT', 'CONNECTION_FAILED']
if (networkErrors.includes(errorCode)) {
  return {
    message: '网络连接不稳定，请检查网络后重试',
    action: 'retry',
    icon: 'wifi-off',
    canRetry: true,
    retryDelay: 3000,
    retryCount: 3,
  }
}
```

#### 2.2 认证错误 (AUTH)
```javascript
// 处理认证相关错误
const authErrors = ['UNAUTHORIZED', 'TOKEN_EXPIRED', 'FORBIDDEN']
if (authErrors.includes(errorCode)) {
  return {
    message: '登录已过期，请重新登录',
    action: 'login',
    icon: 'lock',
    canRetry: false,
    retryDelay: 0,
    loginUrl: '/pages/login/index',
  }
}
```

#### 2.3 解析错误 (PARSE)
```javascript
// 处理输入解析错误
const parseErrors = ['PARSE_ERROR', 'INVALID_INPUT', 'VALIDATION_ERROR']
if (parseErrors.includes(errorCode)) {
  return {
    message: '我没理解您的意思，能换个说法吗？',
    action: 'rephrase',
    icon: 'help-circle',
    canRetry: true,
    retryDelay: 0,
    suggestions: ['请尝试更具体的描述', '可以举个例子说明'],
  }
}
```

#### 2.4 系统错误 (SYSTEM)
```javascript
// 处理系统相关错误
const systemErrors = ['SYSTEM_ERROR', 'SERVICE_UNAVAILABLE', 'RATE_LIMIT']
if (systemErrors.includes(errorCode)) {
  return {
    message: '系统暂时繁忙，请稍后重试',
    action: 'retry',
    icon: 'alert-circle',
    canRetry: true,
    retryDelay: 5000,
    maxRetries: 2,
  }
}
```

### 3. 错误上下文增强

```javascript
/**
 * 根据错误上下文增强错误信息
 * @param {Object} errorConfig - 基础错误配置
 * @param {Object} context - 错误上下文
 * @returns {Object} - 增强后的错误配置
 */
function enhanceErrorWithContext(errorConfig, context) {
  const enhanced = { ...errorConfig }
  
  // 根据用户操作历史调整提示
  if (context.userAction === 'voice_input' && errorConfig.errorType === 'PARSE') {
    enhanced.message = '语音识别可能有误，请重新说一遍或改用文字输入'
    enhanced.suggestions = ['重新录音', '改用文字输入']
  }
  
  // 根据错误频率调整重试策略
  if (context.errorCount > 2) {
    enhanced.canRetry = false
    enhanced.message += '，如问题持续请联系客服'
    enhanced.contactSupport = true
  }
  
  return enhanced
}
```

### 4. 重试策略实现

```javascript
/**
 * 计算重试延迟时间（指数退避）
 * @param {number} retryCount - 当前重试次数
 * @param {number} baseDelay - 基础延迟时间
 * @returns {number} - 计算后的延迟时间
 */
function calculateRetryDelay(retryCount, baseDelay) {
  const maxDelay = 30000 // 最大30秒
  const delay = baseDelay * Math.pow(2, retryCount)
  return Math.min(delay, maxDelay)
}

/**
 * 判断错误是否可以重试
 * @param {string} errorType - 错误类型
 * @param {number} retryCount - 已重试次数
 * @returns {boolean} - 是否可以重试
 */
function canRetryError(errorType, retryCount) {
  const maxRetries = {
    NETWORK: 3,
    SYSTEM: 2,
    PARSE: 1,
    AUTH: 0,
  }
  
  return retryCount < (maxRetries[errorType] || 0)
}
```

### 5. 错误日志记录

```javascript
/**
 * 记录错误日志
 * @param {Object} error - 原始错误
 * @param {Object} mappedError - 映射后的错误
 * @param {Object} context - 错误上下文
 */
function logError(error, mappedError, context) {
  const logData = {
    timestamp: new Date().toISOString(),
    originalError: {
      code: error.code || error.errCode,
      message: error.message,
      stack: error.stack,
    },
    mappedError: {
      type: mappedError.data.errorType,
      message: mappedError.data.message,
      canRetry: mappedError.data.canRetry,
    },
    context: {
      sessionId: context.sessionId,
      userId: context.userId,
      userAgent: context.userAgent,
      retryCount: context.retryCount || 0,
    },
  }
  
  // 根据错误级别选择日志方法
  if (mappedError.data.errorType === 'SYSTEM') {
    console.error('[ErrorMapping] System Error:', logData)
  } else {
    console.warn('[ErrorMapping] User Error:', logData)
  }
}
```

## 验收标准

### 功能验收
- [ ] mapError函数实现完成
- [ ] 支持4种错误类型的映射（NETWORK/AUTH/PARSE/SYSTEM）
- [ ] 返回正确的用户友好错误消息格式
- [ ] 包含合适的操作建议和重试策略
- [ ] 开发模式下保留原始错误信息

### 错误处理验收
- [ ] 未知错误码的默认处理
- [ ] 错误对象格式验证
- [ ] 重试策略的正确实现
- [ ] 错误日志记录完整

### 用户体验验收
- [ ] 错误提示语言友好，避免技术术语
- [ ] 提供明确的解决建议
- [ ] 重试机制合理，不会无限重试
- [ ] 错误图标和操作按钮配置正确

### 性能验收
- [ ] 错误映射处理时间<5ms
- [ ] 内存使用合理
- [ ] 支持高频错误处理

## 依赖关系

### 前置依赖
- 任务-状态映射-01：创建状态映射模块文件（需要ERROR_MAPPING和USER_ERRORS配置）

### 后续依赖
- 任务-状态映射-04：集成状态映射到主处理函数（需要本函数）
- 任务-错误处理-01：设计用户友好错误提示（需要本函数的输出格式）

## 实施步骤

### Step 1: 基础函数实现（1.5小时）
1. 实现mapError函数框架
2. 添加错误码到错误类型的映射逻辑
3. 构建基础的返回消息格式

### Step 2: 错误分类处理（1.5小时）
1. 实现4种错误类型的具体处理逻辑
2. 为每种错误类型设计合适的用户提示
3. 配置相应的操作建议和图标

### Step 3: 重试策略实现（1小时）
1. 实现重试延迟计算逻辑
2. 添加重试次数限制
3. 实现指数退避算法

### Step 4: 上下文增强（1小时）
1. 实现错误上下文分析
2. 根据用户行为调整错误提示
3. 添加错误频率统计和处理

### Step 5: 日志和调试（30分钟）
1. 实现错误日志记录
2. 添加开发模式调试信息
3. 确保生产模式下的信息安全

### Step 6: 测试和验证（30分钟）
1. 测试各种错误场景
2. 验证重试策略的正确性
3. 检查用户提示的友好性

## 注意事项

### 开发注意事项
1. **用户体验优先**: 错误提示要站在用户角度，提供有用的解决建议
2. **安全考虑**: 生产环境不能泄露敏感的技术错误信息
3. **国际化准备**: 错误提示文案要考虑后续的国际化需求
4. **一致性**: 错误提示的语言风格要与产品整体风格一致

### 技术注意事项
1. **性能优化**: 错误处理不能成为性能瓶颈
2. **内存管理**: 避免错误处理过程中的内存泄漏
3. **异常安全**: 错误处理函数本身不能抛出异常
4. **日志规范**: 遵循项目的日志记录和监控规范

### 业务注意事项
1. **重试限制**: 避免无限重试导致的资源浪费
2. **用户引导**: 为不同错误类型提供合适的用户引导
3. **客服对接**: 严重错误要提供客服联系方式
4. **数据统计**: 收集错误统计数据用于产品优化

## 测试建议

### 单元测试用例
1. **错误映射测试**: 测试各种错误码的正确映射
2. **重试策略测试**: 验证重试延迟和次数限制
3. **边界条件测试**: 测试异常输入的处理
4. **调试信息测试**: 验证开发/生产模式的信息处理

### 集成测试用例
1. **端到端错误处理**: 从错误发生到用户看到提示的完整流程
2. **重试机制测试**: 验证自动重试和手动重试的正确性
3. **用户体验测试**: 验证错误提示的友好性和有效性

## 风险评估

### 中等风险
- 错误映射不准确可能导致用户困惑
- 重试策略不当可能影响系统性能

### 风险缓解
- 充分的错误场景测试
- 用户体验测试和反馈收集
- 监控错误处理的效果和性能影响
